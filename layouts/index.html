<!DOCTYPE html>
<html lang="{{ .Site.LanguageCode }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ .Site.Title }}</title>
    <meta name="description" content="{{ .Site.Params.description }}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.3rem;
            font-weight: 300;
            max-width: 800px;
            margin: 0 auto;
            opacity: 0.95;
        }

        .team-section {
            padding: 80px 0;
            background: white;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .team-member {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .member-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 30px;
            overflow: hidden;
            border: 4px solid #667eea;
        }

        .member-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .member-name {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .member-bio {
            font-style: italic;
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 40px 0;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2.5rem;
            }

            .subtitle {
                font-size: 1.1rem;
            }

            .team-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .team-member {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Lost and Loud Creative Studio</h1>
            <p class="subtitle">{{ .Site.Params.description }}</p>
        </div>
    </header>

    <section class="team-section">
        <div class="container">
            <div class="team-grid">
                <div class="team-member">
                    <div class="member-photo">
                        <img src="/images/fermin.jpeg" alt="Fermin Valeros">
                    </div>
                    <h3 class="member-name">Fermin Valeros</h3>
                    <p class="member-bio">Bio coming soon...</p>
                </div>

                <div class="team-member">
                    <div class="member-photo">
                        <img src="/images/veronica.jpeg" alt="Veronica Valeros">
                    </div>
                    <h3 class="member-name">Veronica Valeros</h3>
                    <p class="member-bio">Bio coming soon...</p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>&copy; {{ now.Year }} Lost and Loud Creative Studio. Born and raised in the wild Patagonia.</p>
        </div>
    </footer>
</body>
</html>